#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Stealth ChatGPT Clone - Hidden from Screen Sharing
Uses Gemini API with stealth features like live.py
"""

import sys
import os
import json
import time
import threading
import requests
import ctypes
from datetime import datetime
from typing import List, Dict, Optional

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                 QWidget, QTextEdit, QLineEdit, QPushButton, QFrame, 
                                 QScrollArea, QLabel, QSizeGrip)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
    from PyQt5.QtGui import QFont, QPalette, QColor, QTextCursor
except ImportError as e:
    print(f"Missing PyQt5: {e}")
    print("Install with: pip install PyQt5")
    sys.exit(1)

# Your Gemini API Configuration
GEMINI_API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"

# Stealth Configuration (same as live.py)
WINDOW_TRANSPARENCY = 0.95
ENABLE_SCREEN_CAPTURE_HIDING = True
ENABLE_TASKBAR_HIDING = True
LOCAL_VISIBILITY_MODE = True

class GeminiAPI:
    """Gemini API client for chat responses"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'StealthChatGPT/1.0'
        })
    
    def get_response(self, prompt: str, conversation_history: List[Dict] = None) -> str:
        """Get response from Gemini API"""
        try:
            # Build conversation context
            full_prompt = "You are a helpful AI assistant. Respond naturally and conversationally.\n\n"
            
            if conversation_history:
                for msg in conversation_history[-10:]:  # Last 10 messages for context
                    role = "Human" if msg['role'] == 'user' else "Assistant"
                    full_prompt += f"{role}: {msg['content']}\n"
            
            full_prompt += f"Human: {prompt}\nAssistant:"
            
            payload = {
                "contents": [{"parts": [{"text": full_prompt}]}],
                "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 2000,
                    "topP": 0.9,
                    "topK": 40
                }
            }
            
            response = self.session.post(
                f"{GEMINI_API_URL}?key={self.api_key}",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        return candidate['content']['parts'][0]['text'].strip()
            
            return "Sorry, I couldn't generate a response. Please try again."
            
        except Exception as e:
            return f"Error: {str(e)}"

class ChatWorker(QThread):
    """Worker thread for API calls"""
    response_ready = pyqtSignal(str)
    
    def __init__(self, api_client, prompt, history):
        super().__init__()
        self.api_client = api_client
        self.prompt = prompt
        self.history = history
    
    def run(self):
        response = self.api_client.get_response(self.prompt, self.history)
        self.response_ready.emit(response)

class StealthChatGPT(QMainWindow):
    """Main stealth ChatGPT application"""
    
    def __init__(self):
        super().__init__()
        self.api_client = GeminiAPI(GEMINI_API_KEY)
        self.conversation_history = []
        self.chat_worker = None
        
        self.setup_ui()
        self.apply_stealth_features()
        self.setup_styling()
        
        # Test API connection
        self.test_api_connection()
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("AI Assistant")
        self.setGeometry(100, 100, 500, 700)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        
        # Main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title bar
        title_bar = QFrame()
        title_bar.setFixedHeight(40)
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 5, 10, 5)
        
        title_label = QLabel("🤖 AI Assistant")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_layout.addWidget(title_label)
        
        # Close button
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)
        
        layout.addWidget(title_bar)
        
        # Chat display area
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("Segoe UI", 10))
        layout.addWidget(self.chat_display)
        
        # Input area
        input_frame = QFrame()
        input_layout = QHBoxLayout(input_frame)
        input_layout.setContentsMargins(5, 5, 5, 5)
        
        self.message_input = QLineEdit()
        self.message_input.setFont(QFont("Segoe UI", 10))
        self.message_input.setPlaceholderText("Type your message here...")
        self.message_input.returnPressed.connect(self.send_message)
        
        self.send_button = QPushButton("Send")
        self.send_button.setFixedWidth(80)
        self.send_button.clicked.connect(self.send_message)
        
        input_layout.addWidget(self.message_input)
        input_layout.addWidget(self.send_button)
        layout.addWidget(input_frame)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setFont(QFont("Segoe UI", 8))
        layout.addWidget(self.status_label)
        
        # Size grip for resizing
        size_grip = QSizeGrip(self)
        layout.addWidget(size_grip, 0, Qt.AlignBottom | Qt.AlignRight)
        
        # Add welcome message
        self.add_message("Hello! I'm your AI assistant. How can I help you today?", "assistant")
    
    def apply_stealth_features(self):
        """Apply stealth features to hide from screen sharing"""
        try:
            if sys.platform == "win32":
                hwnd = int(self.winId())
                
                # Hide from screen sharing/recording
                if ENABLE_SCREEN_CAPTURE_HIDING:
                    ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, 0x00000011)
                
                # Hide from Alt+Tab
                if ENABLE_TASKBAR_HIDING:
                    ctypes.windll.user32.SetWindowLongW(
                        hwnd, -20,
                        ctypes.windll.user32.GetWindowLongW(hwnd, -20) | 0x00000080
                    )
                
                print("✅ Stealth features applied - hidden from screen sharing")
            
        except Exception as e:
            print(f"⚠️ Stealth features failed: {e}")
    
    def setup_styling(self):
        """Setup dark theme styling"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QFrame {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 8px;
            }
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 10px;
                color: #ffffff;
            }
            QLineEdit {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
                color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QLabel {
                color: #ffffff;
            }
        """)
        
        # Set window transparency
        self.setWindowOpacity(WINDOW_TRANSPARENCY)
    
    def test_api_connection(self):
        """Test Gemini API connection"""
        self.status_label.setText("Testing API connection...")
        
        def test_connection():
            try:
                response = self.api_client.get_response("Hello", [])
                if "Error:" not in response:
                    self.status_label.setText("✅ API Connected")
                else:
                    self.status_label.setText("❌ API Error")
            except:
                self.status_label.setText("❌ Connection Failed")
        
        threading.Thread(target=test_connection, daemon=True).start()
    
    def add_message(self, message: str, role: str):
        """Add message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        
        if role == "user":
            formatted_message = f'<div style="margin: 10px 0; padding: 10px; background-color: #0078d4; border-radius: 10px; margin-left: 50px;"><b>You</b> <span style="color: #cccccc; font-size: 10px;">{timestamp}</span><br>{message}</div>'
        else:
            formatted_message = f'<div style="margin: 10px 0; padding: 10px; background-color: #404040; border-radius: 10px; margin-right: 50px;"><b>🤖 AI</b> <span style="color: #cccccc; font-size: 10px;">{timestamp}</span><br>{message}</div>'
        
        self.chat_display.insertHtml(formatted_message)
        
        # Scroll to bottom
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.chat_display.setTextCursor(cursor)
        
        # Add to conversation history
        self.conversation_history.append({"role": role, "content": message})
    
    def send_message(self):
        """Send message and get AI response"""
        message = self.message_input.text().strip()
        if not message:
            return
        
        # Add user message
        self.add_message(message, "user")
        self.message_input.clear()
        
        # Disable input while processing
        self.message_input.setEnabled(False)
        self.send_button.setEnabled(False)
        self.status_label.setText("🤖 AI is thinking...")
        
        # Start API call in worker thread
        self.chat_worker = ChatWorker(self.api_client, message, self.conversation_history)
        self.chat_worker.response_ready.connect(self.handle_response)
        self.chat_worker.start()
    
    def handle_response(self, response: str):
        """Handle AI response"""
        self.add_message(response, "assistant")
        
        # Re-enable input
        self.message_input.setEnabled(True)
        self.send_button.setEnabled(True)
        self.message_input.setFocus()
        self.status_label.setText("Ready")
    
    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()

def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("AI Assistant")
    app.setApplicationVersion("1.0")
    
    # Create and show main window
    window = StealthChatGPT()
    window.show()
    
    print("🚀 Stealth ChatGPT Clone started")
    print("🔒 Hidden from screen sharing")
    print("🤖 Powered by Gemini AI")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
