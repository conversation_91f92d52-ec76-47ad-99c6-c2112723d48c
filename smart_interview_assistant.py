#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart Interview Assistant - Real-time Interview Helper
Optimized for fast audio processing and instant AI responses
"""

import sys
import os
import threading
import time
import json
import requests
import ctypes
import numpy as np
from datetime import datetime
from collections import deque
import tempfile
import wave

try:
    # Audio processing
    import pyaudio
    try:
        import pyaudiowpatch as pyaudio_wpatch
        WASAPI_AVAILABLE = True
    except ImportError:
        WASAPI_AVAILABLE = False
    
    # Speech recognition
    import speech_recognition as sr
    
    # GUI
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                 QWidget, QTextEdit, QLabel, QPushButton, QFrame, 
                                 QProgressBar, QComboBox)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt5.QtGui import QFont, QTextCursor
    
except ImportError as e:
    print(f"Missing dependency: {e}")
    print("Install with: pip install PyQt5 pyaudio SpeechRecognition requests numpy")
    sys.exit(1)

# Your Gemini API Configuration
GEMINI_API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"

# Audio Configuration - Optimized for speed
CHUNK = 4096  # Larger chunks for better performance
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000  # Optimal for speech recognition
RECORD_SECONDS = 3  # Process every 3 seconds for real-time feel

class FastGeminiAPI:
    """Optimized Gemini API client for fast responses"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.session = requests.Session()
        # Optimize session for speed
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive'
        })
        
        # Interview-specific prompts for faster, focused responses
        self.interview_context = """You are an expert interview assistant. Provide concise, professional answers for interview questions. 
        Focus on: Technical accuracy, Clear explanations, Professional tone, Practical examples.
        Keep responses under 100 words unless specifically asked for detailed explanation."""
    
    def get_quick_answer(self, question: str, context: str = "") -> str:
        """Get fast, focused interview answer"""
        try:
            # Optimized prompt for speed
            prompt = f"{self.interview_context}\n\nInterview Question: {question}\n\nContext: {context}\n\nProvide a concise, professional answer:"
            
            payload = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.3,  # Lower for consistent, professional responses
                    "maxOutputTokens": 300,  # Shorter for speed
                    "topP": 0.8,
                    "topK": 20,  # Reduced for faster processing
                    "candidateCount": 1
                }
            }
            
            response = self.session.post(
                f"{GEMINI_API_URL}?key={self.api_key}",
                json=payload,
                timeout=10  # Shorter timeout for speed
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        return candidate['content']['parts'][0]['text'].strip()
            
            return "Unable to generate response quickly. Please rephrase the question."
            
        except Exception as e:
            return f"Quick response unavailable: {str(e)[:50]}..."

class AudioProcessor(QThread):
    """Optimized audio processing thread"""
    
    question_detected = pyqtSignal(str)
    audio_level = pyqtSignal(int)
    status_update = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.is_running = False
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Optimize recognizer for speed
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8  # Shorter pause for faster detection
        self.recognizer.phrase_threshold = 0.3
        
        # Audio buffer for continuous processing
        self.audio_buffer = deque(maxlen=10)
        
        # Setup audio capture
        self.setup_audio_capture()
    
    def setup_audio_capture(self):
        """Setup optimized audio capture"""
        try:
            # Try WASAPI first for system audio
            if WASAPI_AVAILABLE:
                self.audio = pyaudio_wpatch.PyAudio()
                self.setup_system_audio()
            else:
                self.audio = pyaudio.PyAudio()
                self.setup_microphone_audio()
                
        except Exception as e:
            self.status_update.emit(f"Audio setup error: {e}")
    
    def setup_system_audio(self):
        """Setup system audio capture (for interviewer's voice)"""
        try:
            # Get default speakers as loopback device
            wasapi_info = self.audio.get_host_api_info_by_type(pyaudio.paWASAPI)
            default_speakers = self.audio.get_device_info_by_index(wasapi_info["defaultOutputDevice"])
            
            # Find loopback device
            for i in range(self.audio.get_device_count()):
                dev_info = self.audio.get_device_info_by_index(i)
                if (dev_info.get("isLoopbackDevice", False) and 
                    default_speakers["name"] in dev_info["name"]):
                    
                    self.stream = self.audio.open(
                        format=FORMAT,
                        channels=CHANNELS,
                        rate=RATE,
                        input=True,
                        input_device_index=i,
                        frames_per_buffer=CHUNK
                    )
                    self.status_update.emit("✅ System audio capture ready")
                    return
            
            # Fallback to microphone
            self.setup_microphone_audio()
            
        except Exception as e:
            self.setup_microphone_audio()
    
    def setup_microphone_audio(self):
        """Fallback microphone audio capture"""
        try:
            self.stream = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            self.status_update.emit("🎤 Microphone capture ready")
        except Exception as e:
            self.status_update.emit(f"❌ Audio capture failed: {e}")
    
    def run(self):
        """Main audio processing loop"""
        self.is_running = True
        audio_data = []
        
        while self.is_running:
            try:
                # Read audio chunk
                data = self.stream.read(CHUNK, exception_on_overflow=False)
                audio_data.append(data)
                
                # Calculate audio level for visual feedback
                audio_np = np.frombuffer(data, dtype=np.int16)
                audio_level = int(np.sqrt(np.mean(audio_np**2)) / 100)
                self.audio_level.emit(min(audio_level, 100))
                
                # Process every 3 seconds of audio
                if len(audio_data) >= (RATE * RECORD_SECONDS) // CHUNK:
                    self.process_audio_chunk(audio_data)
                    audio_data = []
                
            except Exception as e:
                self.status_update.emit(f"Audio processing error: {e}")
                time.sleep(0.1)
    
    def process_audio_chunk(self, audio_data):
        """Process audio chunk for speech recognition"""
        try:
            # Combine audio chunks
            audio_bytes = b''.join(audio_data)
            
            # Create temporary WAV file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                with wave.open(temp_file.name, 'wb') as wav_file:
                    wav_file.setnchannels(CHANNELS)
                    wav_file.setsampwidth(self.audio.get_sample_size(FORMAT))
                    wav_file.setframerate(RATE)
                    wav_file.writeframes(audio_bytes)
                
                # Speech recognition
                with sr.AudioFile(temp_file.name) as source:
                    audio = self.recognizer.record(source)
                    
                try:
                    # Use Google's free speech recognition (fastest)
                    text = self.recognizer.recognize_google(audio, language='en-US')
                    if len(text.strip()) > 10:  # Filter out short/noise
                        self.question_detected.emit(text)
                        
                except sr.UnknownValueError:
                    pass  # No speech detected
                except sr.RequestError as e:
                    self.status_update.emit(f"Speech recognition error: {e}")
                
                # Clean up temp file
                os.unlink(temp_file.name)
                
        except Exception as e:
            self.status_update.emit(f"Audio processing error: {e}")
    
    def stop(self):
        """Stop audio processing"""
        self.is_running = False
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        if hasattr(self, 'audio'):
            self.audio.terminate()

class AnswerGenerator(QThread):
    """Fast answer generation thread"""
    
    answer_ready = pyqtSignal(str, str)  # question, answer
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.question_queue = deque()
        self.is_running = False
    
    def add_question(self, question: str):
        """Add question to processing queue"""
        self.question_queue.append(question)
    
    def run(self):
        """Process questions from queue"""
        self.is_running = True
        
        while self.is_running:
            if self.question_queue:
                question = self.question_queue.popleft()
                answer = self.api_client.get_quick_answer(question)
                self.answer_ready.emit(question, answer)
            else:
                time.sleep(0.1)
    
    def stop(self):
        """Stop answer generation"""
        self.is_running = False

class SmartInterviewAssistant(QMainWindow):
    """Main interview assistant application"""
    
    def __init__(self):
        super().__init__()
        self.api_client = FastGeminiAPI(GEMINI_API_KEY)
        self.audio_processor = None
        self.answer_generator = None
        
        self.setup_ui()
        self.apply_stealth_features()
        self.setup_styling()
        self.start_processing()
    
    def setup_ui(self):
        """Setup user interface"""
        self.setWindowTitle("Smart Interview Assistant")
        self.setGeometry(100, 100, 600, 800)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        
        # Main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Title bar
        title_frame = QFrame()
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(10, 5, 10, 5)
        
        title_label = QLabel("🎯 Smart Interview Assistant")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title_layout.addWidget(title_label)
        
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)
        
        layout.addWidget(title_frame)
        
        # Status panel
        status_frame = QFrame()
        status_layout = QVBoxLayout(status_frame)
        
        self.status_label = QLabel("🔄 Initializing...")
        self.status_label.setFont(QFont("Segoe UI", 10))
        status_layout.addWidget(self.status_label)
        
        # Audio level indicator
        self.audio_progress = QProgressBar()
        self.audio_progress.setMaximum(100)
        self.audio_progress.setTextVisible(False)
        self.audio_progress.setFixedHeight(8)
        status_layout.addWidget(self.audio_progress)
        
        layout.addWidget(status_frame)
        
        # Questions and answers display
        self.qa_display = QTextEdit()
        self.qa_display.setReadOnly(True)
        self.qa_display.setFont(QFont("Segoe UI", 10))
        layout.addWidget(self.qa_display)
        
        # Quick stats
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        
        self.questions_count = QLabel("Questions: 0")
        self.response_time = QLabel("Avg Response: 0s")
        
        stats_layout.addWidget(self.questions_count)
        stats_layout.addWidget(self.response_time)
        
        layout.addWidget(stats_frame)
        
        # Add initial message
        self.add_qa("System", "Smart Interview Assistant is ready! Listening for interview questions...", "system")
    
    def apply_stealth_features(self):
        """Apply stealth features"""
        try:
            if sys.platform == "win32":
                hwnd = int(self.winId())
                
                # Hide from screen capture
                ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, 0x00000011)
                
                # Hide from Alt+Tab
                ctypes.windll.user32.SetWindowLongW(
                    hwnd, -20,
                    ctypes.windll.user32.GetWindowLongW(hwnd, -20) | 0x00000080
                )
                
                print("✅ Stealth mode activated")
        except Exception as e:
            print(f"⚠️ Stealth features failed: {e}")
    
    def setup_styling(self):
        """Setup dark theme styling"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 8px;
                padding: 5px;
            }
            QTextEdit {
                background-color: #0d1117;
                border: 1px solid #30363d;
                border-radius: 8px;
                padding: 10px;
                color: #f0f6fc;
            }
            QLabel {
                color: #f0f6fc;
                padding: 2px;
            }
            QPushButton {
                background-color: #238636;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ea043;
            }
            QProgressBar {
                border: 1px solid #30363d;
                border-radius: 4px;
                background-color: #21262d;
            }
            QProgressBar::chunk {
                background-color: #238636;
                border-radius: 3px;
            }
        """)
        
        self.setWindowOpacity(0.95)
    
    def start_processing(self):
        """Start audio processing and answer generation"""
        # Start audio processor
        self.audio_processor = AudioProcessor()
        self.audio_processor.question_detected.connect(self.handle_question)
        self.audio_processor.audio_level.connect(self.update_audio_level)
        self.audio_processor.status_update.connect(self.update_status)
        self.audio_processor.start()
        
        # Start answer generator
        self.answer_generator = AnswerGenerator(self.api_client)
        self.answer_generator.answer_ready.connect(self.handle_answer)
        self.answer_generator.start()
        
        self.update_status("✅ Ready - Listening for questions...")
    
    def handle_question(self, question: str):
        """Handle detected question"""
        self.update_status(f"🎯 Processing: {question[:50]}...")
        self.add_qa("Question Detected", question, "question")
        
        # Add to answer generation queue
        if self.answer_generator:
            self.answer_generator.add_question(question)
    
    def handle_answer(self, question: str, answer: str):
        """Handle generated answer"""
        self.add_qa("AI Answer", answer, "answer")
        self.update_status("✅ Answer ready!")
        
        # Auto-clear status after 3 seconds
        QTimer.singleShot(3000, lambda: self.update_status("🎧 Listening..."))
    
    def add_qa(self, title: str, content: str, qa_type: str):
        """Add question/answer to display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if qa_type == "question":
            color = "#ffd700"  # Gold for questions
            icon = "❓"
        elif qa_type == "answer":
            color = "#00ff00"  # Green for answers
            icon = "💡"
        else:
            color = "#888888"  # Gray for system messages
            icon = "ℹ️"
        
        formatted_text = f'''
        <div style="margin: 10px 0; padding: 10px; background-color: rgba(45, 45, 45, 0.8); border-left: 4px solid {color}; border-radius: 6px;">
            <b style="color: {color};">{icon} {title}</b> 
            <span style="color: #888; font-size: 10px; float: right;">{timestamp}</span>
            <br><br>
            <span style="color: #f0f6fc; line-height: 1.4;">{content}</span>
        </div>
        '''
        
        self.qa_display.insertHtml(formatted_text)
        
        # Scroll to bottom
        cursor = self.qa_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.qa_display.setTextCursor(cursor)
    
    def update_status(self, status: str):
        """Update status label"""
        self.status_label.setText(status)
    
    def update_audio_level(self, level: int):
        """Update audio level indicator"""
        self.audio_progress.setValue(level)
    
    def closeEvent(self, event):
        """Handle application close"""
        if self.audio_processor:
            self.audio_processor.stop()
        if self.answer_generator:
            self.answer_generator.stop()
        event.accept()
    
    def mousePressEvent(self, event):
        """Handle mouse press for dragging"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()

def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Create and show main window
    window = SmartInterviewAssistant()
    window.show()
    
    print("🎯 Smart Interview Assistant started")
    print("🔒 Stealth mode: Hidden from screen sharing")
    print("🎧 Audio processing: Optimized for real-time")
    print("🤖 AI responses: Fast and focused")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
