#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini API Usage Examples
Demonstrates various ways to use the Gemini API service
"""

from gemini_api_service import GeminiAPIService
import time

def main():
    """Main function demonstrating Gemini API usage"""
    
    # Your API key
    API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
    
    print("🚀 Initializing Gemini API Service...")
    gemini = GeminiAPIService(API_KEY)
    
    # Test connection first
    print("\n🔍 Testing API connection...")
    if not gemini.test_connection():
        print("❌ Failed to connect to Gemini API. Please check your API key.")
        return
    
    print("✅ Connection successful! Starting examples...\n")
    
    # Example 1: Simple Question Answering
    print("=" * 60)
    print("📝 Example 1: Simple Question Answering")
    print("=" * 60)
    
    question = "What is Python programming language? Explain in simple terms."
    print(f"Question: {question}")
    print("\nGenerating response...")
    
    response = gemini.generate_content(
        prompt=question,
        model='gemini-1.5-flash',
        temperature=0.7,
        max_tokens=300
    )
    
    if response:
        print(f"\n🤖 Gemini Response:\n{response}")
    else:
        print("❌ Failed to get response")
    
    time.sleep(2)  # Small delay between requests
    
    # Example 2: Code Generation
    print("\n" + "=" * 60)
    print("💻 Example 2: Code Generation")
    print("=" * 60)
    
    code_request = """
    Write a Python function that:
    1. Takes a list of numbers as input
    2. Returns the sum of all even numbers in the list
    3. Include proper error handling
    4. Add comments explaining the code
    """
    
    print(f"Request: {code_request}")
    print("\nGenerating code...")
    
    code_response = gemini.generate_content(
        prompt=code_request,
        model='gemini-1.5-flash',
        temperature=0.3,  # Lower temperature for more consistent code
        max_tokens=500
    )
    
    if code_response:
        print(f"\n🤖 Generated Code:\n{code_response}")
    else:
        print("❌ Failed to generate code")
    
    time.sleep(2)
    
    # Example 3: Chat Conversation
    print("\n" + "=" * 60)
    print("💬 Example 3: Chat Conversation")
    print("=" * 60)
    
    # Simulate a conversation
    conversation = [
        {"role": "system", "content": "You are a helpful programming tutor."},
        {"role": "user", "content": "I'm learning JavaScript. What are variables?"},
    ]
    
    print("Starting conversation...")
    for msg in conversation:
        if msg["role"] == "user":
            print(f"👤 User: {msg['content']}")
        elif msg["role"] == "system":
            print(f"🔧 System: {msg['content']}")
    
    chat_response = gemini.chat_completion(
        messages=conversation,
        model='gemini-1.5-flash',
        temperature=0.6,
        max_tokens=400
    )
    
    if chat_response:
        print(f"\n🤖 Tutor: {chat_response}")
        
        # Continue the conversation
        conversation.append({"role": "assistant", "content": chat_response})
        conversation.append({"role": "user", "content": "Can you show me an example of declaring variables?"})
        
        print(f"\n👤 User: Can you show me an example of declaring variables?")
        
        follow_up_response = gemini.chat_completion(
            messages=conversation,
            model='gemini-1.5-flash',
            temperature=0.6,
            max_tokens=300
        )
        
        if follow_up_response:
            print(f"\n🤖 Tutor: {follow_up_response}")
    else:
        print("❌ Failed to get chat response")
    
    time.sleep(2)
    
    # Example 4: Creative Writing
    print("\n" + "=" * 60)
    print("✨ Example 4: Creative Writing")
    print("=" * 60)
    
    creative_prompt = """
    Write a short story (2-3 paragraphs) about a programmer who discovers 
    that their code has come to life. Make it interesting and engaging.
    """
    
    print(f"Creative Prompt: {creative_prompt}")
    print("\nGenerating story...")
    
    story_response = gemini.generate_content(
        prompt=creative_prompt,
        model='gemini-1.5-flash',
        temperature=0.9,  # Higher temperature for creativity
        max_tokens=600
    )
    
    if story_response:
        print(f"\n📖 Generated Story:\n{story_response}")
    else:
        print("❌ Failed to generate story")
    
    time.sleep(2)
    
    # Example 5: Technical Explanation
    print("\n" + "=" * 60)
    print("🔬 Example 5: Technical Explanation")
    print("=" * 60)
    
    technical_question = """
    Explain the concept of machine learning in simple terms that a beginner 
    can understand. Include:
    1. What it is
    2. How it works (basic concept)
    3. A real-world example
    4. Why it's useful
    """
    
    print(f"Technical Question: {technical_question}")
    print("\nGenerating explanation...")
    
    explanation_response = gemini.generate_content(
        prompt=technical_question,
        model='gemini-1.5-pro',  # Using Pro model for more detailed explanation
        temperature=0.5,
        max_tokens=700
    )
    
    if explanation_response:
        print(f"\n🧠 Technical Explanation:\n{explanation_response}")
    else:
        print("❌ Failed to generate explanation")
    
    # Show performance statistics
    print("\n" + "=" * 60)
    print("📊 Performance Statistics")
    print("=" * 60)
    
    stats = gemini.get_performance_stats()
    print(f"Total Requests: {stats['total_requests']}")
    print(f"Successful Requests: {stats['successful_requests']}")
    print(f"Success Rate: {stats['success_rate']:.1f}%")
    print(f"Average Response Time: {stats['avg_response_time']:.2f} seconds")
    print(f"Fastest Response: {stats['min_response_time']:.2f} seconds")
    print(f"Slowest Response: {stats['max_response_time']:.2f} seconds")
    
    # List available models
    print("\n" + "=" * 60)
    print("🤖 Available Models")
    print("=" * 60)
    
    models = gemini.list_models()
    print("Available Gemini models:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")
    
    print("\n✅ All examples completed successfully!")
    print("\n💡 Tips for using Gemini API:")
    print("- Use lower temperature (0.1-0.3) for factual/code responses")
    print("- Use higher temperature (0.7-0.9) for creative content")
    print("- gemini-1.5-flash is faster and cheaper")
    print("- gemini-1.5-pro is more capable for complex tasks")
    print("- Always handle potential API failures gracefully")


def interactive_chat():
    """Interactive chat session with Gemini"""
    
    API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
    gemini = GeminiAPIService(API_KEY)
    
    print("🤖 Interactive Gemini Chat")
    print("Type 'quit' to exit, 'stats' to see performance stats")
    print("=" * 50)
    
    if not gemini.test_connection():
        print("❌ Failed to connect to Gemini API")
        return
    
    conversation = [
        {"role": "system", "content": "You are a helpful AI assistant. Be concise but informative."}
    ]
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() == 'stats':
                stats = gemini.get_performance_stats()
                print(f"\n📊 Stats: {stats['total_requests']} requests, "
                      f"{stats['success_rate']:.1f}% success rate, "
                      f"{stats['avg_response_time']:.2f}s avg response time")
                continue
            
            if not user_input:
                continue
            
            conversation.append({"role": "user", "content": user_input})
            
            print("🤖 Gemini: ", end="", flush=True)
            
            response = gemini.chat_completion(
                messages=conversation,
                model='gemini-1.5-flash',
                temperature=0.7,
                max_tokens=500
            )
            
            if response:
                print(response)
                conversation.append({"role": "assistant", "content": response})
            else:
                print("❌ Sorry, I couldn't generate a response. Please try again.")
        
        except KeyboardInterrupt:
            print("\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    print("Choose an option:")
    print("1. Run all examples")
    print("2. Interactive chat")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        interactive_chat()
    else:
        print("Invalid choice. Running all examples...")
        main()
