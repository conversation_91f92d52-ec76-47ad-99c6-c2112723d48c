#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration file for API keys and settings
"""

import os
from typing import Dict, Any

# API Configuration
API_KEYS = {
    # Your Gemini API key
    'GEMINI_API_KEY': 'AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE',
    
    # Add other API keys here as needed
    # 'OPENAI_API_KEY': 'your-openai-key-here',
    # 'ANTHROPIC_API_KEY': 'your-anthropic-key-here',
}

# Gemini API Settings
GEMINI_CONFIG = {
    'base_url': 'https://generativelanguage.googleapis.com/v1beta',
    'default_model': 'gemini-1.5-flash',
    'default_temperature': 0.7,
    'default_max_tokens': 1000,
    'timeout': 30,
    'max_retries': 3,
}

# Available Gemini Models
GEMINI_MODELS = {
    'flash': 'gemini-1.5-flash-latest',      # Fast and efficient
    'pro': 'gemini-1.5-pro-latest',          # More capable
    'legacy': 'gemini-1.0-pro-latest'        # Legacy model
}

# Application Settings
APP_CONFIG = {
    'debug': False,
    'log_level': 'INFO',
    'max_conversation_history': 50,
    'auto_save_conversations': True,
    'conversation_save_path': './conversations/',
}

# Performance Settings
PERFORMANCE_CONFIG = {
    'connection_pool_size': 10,
    'max_concurrent_requests': 5,
    'request_timeout': 30,
    'retry_delay': 1.0,
    'max_response_cache_size': 100,
}

def get_api_key(service: str) -> str:
    """
    Get API key for a specific service
    
    Args:
        service: Service name (e.g., 'GEMINI_API_KEY')
        
    Returns:
        API key string
        
    Raises:
        ValueError: If API key not found
    """
    # First check environment variables
    env_key = os.getenv(service)
    if env_key:
        return env_key
    
    # Then check config
    config_key = API_KEYS.get(service)
    if config_key:
        return config_key
    
    raise ValueError(f"API key for {service} not found in environment or config")

def get_gemini_api_key() -> str:
    """Get Gemini API key"""
    return get_api_key('GEMINI_API_KEY')

def get_config(section: str) -> Dict[str, Any]:
    """
    Get configuration for a specific section
    
    Args:
        section: Configuration section name
        
    Returns:
        Configuration dictionary
    """
    configs = {
        'gemini': GEMINI_CONFIG,
        'app': APP_CONFIG,
        'performance': PERFORMANCE_CONFIG,
    }
    
    return configs.get(section, {})

def validate_config() -> bool:
    """
    Validate configuration settings
    
    Returns:
        True if configuration is valid, False otherwise
    """
    try:
        # Check if Gemini API key exists
        gemini_key = get_gemini_api_key()
        if not gemini_key or len(gemini_key) < 10:
            print("❌ Invalid Gemini API key")
            return False
        
        # Check if required directories exist
        conv_path = APP_CONFIG.get('conversation_save_path', './conversations/')
        if not os.path.exists(conv_path):
            os.makedirs(conv_path, exist_ok=True)
            print(f"✅ Created conversation directory: {conv_path}")
        
        print("✅ Configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

# Environment-specific overrides
def load_environment_config():
    """Load configuration from environment variables"""
    
    # Override API keys from environment
    for key in API_KEYS.keys():
        env_value = os.getenv(key)
        if env_value:
            API_KEYS[key] = env_value
            print(f"✅ Loaded {key} from environment")
    
    # Override app settings from environment
    debug_env = os.getenv('DEBUG', '').lower()
    if debug_env in ['true', '1', 'yes']:
        APP_CONFIG['debug'] = True
    
    log_level_env = os.getenv('LOG_LEVEL')
    if log_level_env:
        APP_CONFIG['log_level'] = log_level_env.upper()

# Load environment config on import
load_environment_config()

# Example usage
if __name__ == "__main__":
    print("🔧 Configuration Test")
    print("=" * 40)
    
    # Validate configuration
    if validate_config():
        print("\n✅ Configuration is valid!")
        
        # Show current configuration
        print(f"\n📋 Current Configuration:")
        print(f"Gemini API Key: {'*' * 20}{get_gemini_api_key()[-10:]}")
        print(f"Default Model: {GEMINI_CONFIG['default_model']}")
        print(f"Debug Mode: {APP_CONFIG['debug']}")
        print(f"Log Level: {APP_CONFIG['log_level']}")
        
        print(f"\n🤖 Available Models:")
        for name, model in GEMINI_MODELS.items():
            print(f"  {name}: {model}")
        
    else:
        print("\n❌ Configuration validation failed!")
        print("Please check your API keys and settings.")
