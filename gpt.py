#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatGPT Clone using Gemini API
A complete chat application with web interface
"""

import requests
import json
import time
from datetime import datetime
from flask import Flask, render_template_string, request, jsonify, session
import secrets
import threading
import webbrowser
from typing import List, Dict, Optional

# Your Gemini API Key
GEMINI_API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"

# Flask app setup
app = Flask(__name__)
app.secret_key = secrets.token_hex(16)

# Global conversation storage
conversations = {}

class GeminiChatBot:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def generate_response(self, prompt: str, conversation_history: List[Dict] = None) -> str:
        """Generate response using Gemini API"""
        try:
            # Prepare conversation context
            full_prompt = ""
            if conversation_history:
                for msg in conversation_history[-10:]:  # Last 10 messages for context
                    role = "Human" if msg['role'] == 'user' else "Assistant"
                    full_prompt += f"{role}: {msg['content']}\n"
            
            full_prompt += f"Human: {prompt}\nAssistant:"
            
            # API payload
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": full_prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 2000,
                    "topP": 0.9,
                    "topK": 40
                }
            }
            
            # Make API request
            response = self.session.post(
                f"{GEMINI_API_URL}?key={self.api_key}",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        generated_text = candidate['content']['parts'][0]['text']
                        return generated_text.strip()
            
            return "Sorry, I couldn't generate a response. Please try again."
            
        except Exception as e:
            return f"Error: {str(e)}"

# Initialize chatbot
chatbot = GeminiChatBot(GEMINI_API_KEY)

# HTML Template for the chat interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT Clone - Gemini AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #343541;
            color: #fff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #202123;
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #4a4a4a;
        }
        
        .header h1 {
            color: #10a37f;
            font-size: 1.5rem;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .message {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .message.user {
            background: #444654;
            margin-left: 2rem;
        }
        
        .message.assistant {
            background: #343541;
            margin-right: 2rem;
            border: 1px solid #4a4a4a;
        }
        
        .message-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .user .message-avatar {
            background: #10a37f;
        }
        
        .assistant .message-avatar {
            background: #da70d6;
        }
        
        .message-content {
            flex: 1;
            line-height: 1.6;
            white-space: pre-wrap;
        }
        
        .input-container {
            padding: 1rem;
            background: #40414f;
            border-top: 1px solid #4a4a4a;
        }
        
        .input-form {
            display: flex;
            gap: 0.5rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .input-field {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 1px solid #4a4a4a;
            border-radius: 6px;
            background: #40414f;
            color: #fff;
            font-size: 1rem;
            outline: none;
        }
        
        .input-field:focus {
            border-color: #10a37f;
        }
        
        .send-button {
            padding: 0.75rem 1.5rem;
            background: #10a37f;
            color: #fff;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.2s;
        }
        
        .send-button:hover {
            background: #0d8f6f;
        }
        
        .send-button:disabled {
            background: #4a4a4a;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            padding: 1rem;
            color: #888;
            font-style: italic;
        }
        
        .clear-button {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            background: #d73a49;
            color: #fff;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .clear-button:hover {
            background: #cb2431;
        }
        
        @media (max-width: 768px) {
            .message.user {
                margin-left: 0;
            }
            
            .message.assistant {
                margin-right: 0;
            }
            
            .input-form {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 ChatGPT Clone - Powered by Gemini AI</h1>
    </div>
    
    <button class="clear-button" onclick="clearChat()">Clear Chat</button>
    
    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="message assistant">
                <div class="message-avatar">🤖</div>
                <div class="message-content">Hello! I'm your AI assistant powered by Google's Gemini AI. How can I help you today?</div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typing">
            🤖 AI is typing...
        </div>
        
        <div class="input-container">
            <form class="input-form" onsubmit="sendMessage(event)">
                <input 
                    type="text" 
                    class="input-field" 
                    id="messageInput" 
                    placeholder="Type your message here..." 
                    autocomplete="off"
                    required
                >
                <button type="submit" class="send-button" id="sendButton">Send</button>
            </form>
        </div>
    </div>

    <script>
        let conversationHistory = [];
        
        function addMessage(content, role) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = role === 'user' ? '👤' : '🤖';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${content}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Add to conversation history
            conversationHistory.push({role: role, content: content});
        }
        
        function showTyping() {
            document.getElementById('typing').style.display = 'block';
            document.getElementById('messages').scrollTop = document.getElementById('messages').scrollHeight;
        }
        
        function hideTyping() {
            document.getElementById('typing').style.display = 'none';
        }
        
        async function sendMessage(event) {
            event.preventDefault();
            
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input and disable button
            input.value = '';
            sendButton.disabled = true;
            showTyping();
            
            try {
                // Send to backend
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        history: conversationHistory
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addMessage(data.response, 'assistant');
                } else {
                    addMessage('Sorry, there was an error processing your request.', 'assistant');
                }
                
            } catch (error) {
                addMessage('Sorry, there was a connection error.', 'assistant');
            } finally {
                hideTyping();
                sendButton.disabled = false;
                input.focus();
            }
        }
        
        function clearChat() {
            if (confirm('Are you sure you want to clear the chat?')) {
                document.getElementById('messages').innerHTML = `
                    <div class="message assistant">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">Hello! I'm your AI assistant powered by Google's Gemini AI. How can I help you today?</div>
                    </div>
                `;
                conversationHistory = [];
            }
        }
        
        // Focus input on page load
        document.getElementById('messageInput').focus();
        
        // Handle Enter key
        document.getElementById('messageInput').addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage(event);
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main chat interface"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/chat', methods=['POST'])
def chat():
    """Handle chat messages"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        history = data.get('history', [])
        
        if not message:
            return jsonify({'success': False, 'error': 'No message provided'})
        
        # Generate response using Gemini
        response = chatbot.generate_response(message, history)
        
        return jsonify({
            'success': True,
            'response': response,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def open_browser():
    """Open browser after a short delay"""
    time.sleep(1.5)
    webbrowser.open('http://localhost:5000')

if __name__ == '__main__':
    print("🚀 Starting ChatGPT Clone with Gemini AI...")
    print("🔑 API Key configured")
    print("🌐 Opening browser...")
    
    # Open browser in a separate thread
    threading.Thread(target=open_browser, daemon=True).start()
    
    # Start Flask app
    app.run(debug=False, host='0.0.0.0', port=5000)
