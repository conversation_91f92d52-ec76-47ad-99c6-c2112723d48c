#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini API Service
A dedicated service for interacting with Google's Gemini AI API
"""

import requests
import json
import time
from typing import Optional, Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiAPIService:
    """
    A comprehensive service for interacting with Google's Gemini AI API
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the Gemini API service
        
        Args:
            api_key: Your Google AI Studio API key
        """
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.session = requests.Session()
        
        # Configure session for optimal performance
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'GeminiAPIService/1.0'
        })
        
        # Performance tracking
        self.response_times = []
        self.total_requests = 0
        self.successful_requests = 0
        
        # Available models
        self.models = {
            'gemini-1.5-flash': 'gemini-1.5-flash-latest',
            'gemini-1.5-pro': 'gemini-1.5-pro-latest',
            'gemini-1.0-pro': 'gemini-1.0-pro-latest'
        }
        
        logger.info("Gemini API Service initialized successfully")
    
    def generate_content(self, 
                        prompt: str, 
                        model: str = 'gemini-1.5-flash',
                        temperature: float = 0.7,
                        max_tokens: int = 1000,
                        top_p: float = 0.9,
                        top_k: int = 40) -> Optional[str]:
        """
        Generate content using Gemini API
        
        Args:
            prompt: The input text prompt
            model: Model to use ('gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-1.0-pro')
            temperature: Controls randomness (0.0 to 1.0)
            max_tokens: Maximum tokens to generate
            top_p: Nucleus sampling parameter
            top_k: Top-k sampling parameter
            
        Returns:
            Generated text response or None if failed
        """
        try:
            start_time = time.time()
            self.total_requests += 1
            
            # Get the full model name
            model_name = self.models.get(model, 'gemini-1.5-flash-latest')
            
            # Construct the API endpoint
            endpoint = f"{self.base_url}/models/{model_name}:generateContent"
            
            # Prepare the request payload
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": temperature,
                    "maxOutputTokens": max_tokens,
                    "topP": top_p,
                    "topK": top_k,
                    "candidateCount": 1
                }
            }
            
            # Add API key to URL
            params = {'key': self.api_key}
            
            logger.info(f"Sending request to Gemini API using model: {model_name}")
            
            # Make the API request
            response = self.session.post(
                endpoint,
                json=payload,
                params=params,
                timeout=30
            )
            
            # Calculate response time
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            
            # Check if request was successful
            if response.status_code == 200:
                self.successful_requests += 1
                result = response.json()
                
                # Extract the generated text
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        generated_text = candidate['content']['parts'][0]['text']
                        logger.info(f"Successfully generated content in {response_time:.2f}s")
                        return generated_text.strip()
                
                logger.warning("No content found in API response")
                return None
                
            else:
                logger.error(f"API request failed with status {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("API request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return None
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: str = 'gemini-1.5-flash',
                       temperature: float = 0.7,
                       max_tokens: int = 1000) -> Optional[str]:
        """
        Chat completion with conversation history
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use
            temperature: Controls randomness
            max_tokens: Maximum tokens to generate
            
        Returns:
            Generated response or None if failed
        """
        try:
            # Convert messages to Gemini format
            contents = []
            
            for message in messages:
                role = message.get('role', 'user')
                content = message.get('content', '')
                
                # Gemini uses 'user' and 'model' roles
                if role == 'assistant':
                    role = 'model'
                elif role == 'system':
                    # System messages are treated as user messages in Gemini
                    role = 'user'
                    content = f"System: {content}"
                
                contents.append({
                    "role": role,
                    "parts": [{"text": content}]
                })
            
            # Use the generateContent endpoint with conversation
            model_name = self.models.get(model, 'gemini-1.5-flash-latest')
            endpoint = f"{self.base_url}/models/{model_name}:generateContent"
            
            payload = {
                "contents": contents,
                "generationConfig": {
                    "temperature": temperature,
                    "maxOutputTokens": max_tokens,
                    "candidateCount": 1
                }
            }
            
            params = {'key': self.api_key}
            
            response = self.session.post(
                endpoint,
                json=payload,
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        return candidate['content']['parts'][0]['text'].strip()
            
            logger.error(f"Chat completion failed: {response.status_code} - {response.text}")
            return None
            
        except Exception as e:
            logger.error(f"Chat completion error: {e}")
            return None
    
    def list_models(self) -> List[str]:
        """
        List available models
        
        Returns:
            List of available model names
        """
        try:
            endpoint = f"{self.base_url}/models"
            params = {'key': self.api_key}
            
            response = self.session.get(endpoint, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                models = []
                
                if 'models' in result:
                    for model in result['models']:
                        if 'name' in model:
                            model_name = model['name'].replace('models/', '')
                            models.append(model_name)
                
                return models
            
            logger.error(f"Failed to list models: {response.status_code}")
            return list(self.models.values())
            
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return list(self.models.values())
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics
        
        Returns:
            Dictionary with performance metrics
        """
        if not self.response_times:
            return {
                'total_requests': self.total_requests,
                'successful_requests': self.successful_requests,
                'success_rate': 0.0,
                'avg_response_time': 0.0,
                'min_response_time': 0.0,
                'max_response_time': 0.0
            }
        
        avg_time = sum(self.response_times) / len(self.response_times)
        min_time = min(self.response_times)
        max_time = max(self.response_times)
        success_rate = (self.successful_requests / self.total_requests) * 100 if self.total_requests > 0 else 0
        
        return {
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'success_rate': success_rate,
            'avg_response_time': avg_time,
            'min_response_time': min_time,
            'max_response_time': max_time
        }
    
    def test_connection(self) -> bool:
        """
        Test the API connection
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            logger.info("Testing Gemini API connection...")
            
            test_prompt = "Hello, this is a test message. Please respond with 'Connection successful'."
            response = self.generate_content(
                prompt=test_prompt,
                model='gemini-1.5-flash',
                temperature=0.1,
                max_tokens=50
            )
            
            if response:
                logger.info("✅ Gemini API connection test successful")
                return True
            else:
                logger.error("❌ Gemini API connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Connection test error: {e}")
            return False


# Example usage and testing
if __name__ == "__main__":
    # Initialize with your API key
    API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
    
    # Create service instance
    gemini_service = GeminiAPIService(API_KEY)
    
    # Test connection
    if gemini_service.test_connection():
        print("✅ Gemini API is working!")
        
        # Example: Simple text generation
        print("\n--- Simple Text Generation ---")
        response = gemini_service.generate_content(
            prompt="Explain what is React.js in simple terms with a code example.",
            model='gemini-1.5-flash',
            temperature=0.7,
            max_tokens=500
        )
        
        if response:
            print("Response:", response)
        
        # Example: Chat completion
        print("\n--- Chat Completion ---")
        messages = [
            {"role": "system", "content": "You are a helpful programming assistant."},
            {"role": "user", "content": "What is the difference between let and var in JavaScript?"}
        ]
        
        chat_response = gemini_service.chat_completion(
            messages=messages,
            model='gemini-1.5-flash',
            temperature=0.5
        )
        
        if chat_response:
            print("Chat Response:", chat_response)
        
        # Show performance stats
        print("\n--- Performance Stats ---")
        stats = gemini_service.get_performance_stats()
        for key, value in stats.items():
            print(f"{key}: {value}")
        
        # List available models
        print("\n--- Available Models ---")
        models = gemini_service.list_models()
        for model in models:
            print(f"- {model}")
    
    else:
        print("❌ Failed to connect to Gemini API. Please check your API key.")
